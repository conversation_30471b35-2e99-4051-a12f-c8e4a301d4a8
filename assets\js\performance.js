// Performance Optimization JavaScript
// تحسين الأداء

// Lazy Loading for Images
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Service Worker Registration
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered: ', registration);
                })
                .catch(registrationError => {
                    console.log('SW registration failed: ', registrationError);
                });
        });
    }
}

// Cache Management
class CacheManager {
    constructor() {
        this.cacheName = 'useful-tools-cache-v1';
        this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
    }

    async set(key, data) {
        const item = {
            data: data,
            timestamp: Date.now()
        };
        localStorage.setItem(key, JSON.stringify(item));
    }

    async get(key) {
        const item = localStorage.getItem(key);
        if (!item) return null;

        const parsed = JSON.parse(item);
        const now = Date.now();

        if (now - parsed.timestamp > this.cacheExpiry) {
            localStorage.removeItem(key);
            return null;
        }

        return parsed.data;
    }

    clear() {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('useful-tools-')) {
                localStorage.removeItem(key);
            }
        });
    }
}

// Performance Monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.startTime = performance.now();
    }

    mark(name) {
        this.metrics[name] = performance.now();
    }

    measure(name, startMark, endMark) {
        const start = this.metrics[startMark] || this.startTime;
        const end = this.metrics[endMark] || performance.now();
        return end - start;
    }

    getMetrics() {
        return {
            ...this.metrics,
            totalTime: performance.now() - this.startTime,
            memoryUsage: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }
}

// Resource Preloader
class ResourcePreloader {
    constructor() {
        this.preloadedResources = new Set();
    }

    preloadCSS(href) {
        if (this.preloadedResources.has(href)) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
        };
        document.head.appendChild(link);
        this.preloadedResources.add(href);
    }

    preloadJS(src) {
        if (this.preloadedResources.has(src)) return;

        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'script';
        link.href = src;
        document.head.appendChild(link);
        this.preloadedResources.add(src);
    }

    preloadImage(src) {
        if (this.preloadedResources.has(src)) return;

        const img = new Image();
        img.src = src;
        this.preloadedResources.add(src);
    }
}

// Debounce Function
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Throttle Function
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Critical Resource Loading
function loadCriticalResources() {
    const preloader = new ResourcePreloader();
    
    // Preload critical CSS
    preloader.preloadCSS('https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css');
    preloader.preloadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
    
    // Preload critical JS
    preloader.preloadJS('https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js');
}

// Optimize Images
function optimizeImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        // Add loading="lazy" for better performance
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }
        
        // Add proper alt text if missing
        if (!img.hasAttribute('alt')) {
            img.setAttribute('alt', 'صورة');
        }
    });
}

// Memory Management
function cleanupMemory() {
    // Clear unused variables
    if (window.gc && typeof window.gc === 'function') {
        window.gc();
    }
    
    // Clear old cache entries
    const cacheManager = new CacheManager();
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
        if (key.startsWith('useful-tools-')) {
            const item = localStorage.getItem(key);
            if (item) {
                const parsed = JSON.parse(item);
                const now = Date.now();
                if (now - parsed.timestamp > cacheManager.cacheExpiry) {
                    localStorage.removeItem(key);
                }
            }
        }
    });
}

// Initialize Performance Optimizations
function initPerformanceOptimizations() {
    // Initialize lazy loading
    initLazyLoading();
    
    // Register service worker
    registerServiceWorker();
    
    // Load critical resources
    loadCriticalResources();
    
    // Optimize images
    optimizeImages();
    
    // Setup periodic cleanup
    setInterval(cleanupMemory, 5 * 60 * 1000); // Every 5 minutes
    
    // Monitor performance
    const monitor = new PerformanceMonitor();
    monitor.mark('init-complete');
    
    // Log performance metrics in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setTimeout(() => {
            console.log('Performance Metrics:', monitor.getMetrics());
        }, 2000);
    }
}

// Export functions
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CacheManager,
        PerformanceMonitor,
        ResourcePreloader,
        debounce,
        throttle,
        initPerformanceOptimizations
    };
} else if (typeof window !== 'undefined') {
    window.PerformanceUtils = {
        CacheManager,
        PerformanceMonitor,
        ResourcePreloader,
        debounce,
        throttle,
        initPerformanceOptimizations
    };
}

// Auto-initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', initPerformanceOptimizations);
