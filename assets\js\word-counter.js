// Word Counter JavaScript
// عداد الكلمات والأحرف

// Main word counter function
function countWords(text) {
    if (!text || typeof text !== 'string') {
        return getEmptyStats();
    }

    const stats = {
        characters: text.length,
        charactersNoSpaces: text.replace(/\s/g, '').length,
        words: countWordsInText(text),
        sentences: countSentences(text),
        paragraphs: countParagraphs(text),
        lines: countLines(text),
        arabicWords: countArabicWords(text),
        englishWords: countEnglishWords(text),
        numbers: countNumbers(text),
        punctuation: countPunctuation(text),
        spaces: countSpaces(text),
        averageWordsPerSentence: 0,
        averageCharactersPerWord: 0,
        readingTime: 0,
        speakingTime: 0
    };

    // Calculate averages
    if (stats.sentences > 0) {
        stats.averageWordsPerSentence = Math.round(stats.words / stats.sentences * 100) / 100;
    }

    if (stats.words > 0) {
        stats.averageCharactersPerWord = Math.round(stats.charactersNoSpaces / stats.words * 100) / 100;
    }

    // Calculate reading and speaking time (Arabic: 200 WPM reading, 150 WPM speaking)
    if (stats.words > 0) {
        stats.readingTime = Math.ceil(stats.words / 200); // minutes
        stats.speakingTime = Math.ceil(stats.words / 150); // minutes
    }

    return stats;
}

// Count words in text
function countWordsInText(text) {
    if (!text.trim()) return 0;
    
    // Remove extra whitespace and split by whitespace
    const words = text.trim().replace(/\s+/g, ' ').split(' ');
    
    // Filter out empty strings
    return words.filter(word => word.length > 0).length;
}

// Count sentences
function countSentences(text) {
    if (!text.trim()) return 0;
    
    // Arabic and English sentence endings
    const sentenceEndings = /[.!?؟।]/g;
    const matches = text.match(sentenceEndings);
    
    return matches ? matches.length : 1;
}

// Count paragraphs
function countParagraphs(text) {
    if (!text.trim()) return 0;
    
    // Split by double line breaks or more
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    return paragraphs.length;
}

// Count lines
function countLines(text) {
    if (!text) return 0;
    
    return text.split('\n').length;
}

// Count Arabic words
function countArabicWords(text) {
    if (!text) return 0;
    
    // Arabic Unicode range
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+/g;
    const arabicMatches = text.match(arabicRegex);
    
    if (!arabicMatches) return 0;
    
    // Count words in Arabic matches
    let count = 0;
    arabicMatches.forEach(match => {
        const words = match.trim().split(/\s+/).filter(word => word.length > 0);
        count += words.length;
    });
    
    return count;
}

// Count English words
function countEnglishWords(text) {
    if (!text) return 0;
    
    // English letters and common punctuation
    const englishRegex = /[a-zA-Z]+/g;
    const englishMatches = text.match(englishRegex);
    
    return englishMatches ? englishMatches.length : 0;
}

// Count numbers
function countNumbers(text) {
    if (!text) return 0;
    
    // Arabic and English numbers
    const numberRegex = /[\d\u0660-\u0669]+/g;
    const numberMatches = text.match(numberRegex);
    
    return numberMatches ? numberMatches.length : 0;
}

// Count punctuation marks
function countPunctuation(text) {
    if (!text) return 0;
    
    // Common punctuation marks in Arabic and English
    const punctuationRegex = /[.,;:!?؟،؛]/g;
    const punctuationMatches = text.match(punctuationRegex);
    
    return punctuationMatches ? punctuationMatches.length : 0;
}

// Count spaces
function countSpaces(text) {
    if (!text) return 0;
    
    const spaceMatches = text.match(/\s/g);
    return spaceMatches ? spaceMatches.length : 0;
}

// Get empty statistics object
function getEmptyStats() {
    return {
        characters: 0,
        charactersNoSpaces: 0,
        words: 0,
        sentences: 0,
        paragraphs: 0,
        lines: 0,
        arabicWords: 0,
        englishWords: 0,
        numbers: 0,
        punctuation: 0,
        spaces: 0,
        averageWordsPerSentence: 0,
        averageCharactersPerWord: 0,
        readingTime: 0,
        speakingTime: 0
    };
}

// Format reading time
function formatReadingTime(minutes) {
    if (minutes === 0) return 'أقل من دقيقة';
    if (minutes === 1) return 'دقيقة واحدة';
    if (minutes === 2) return 'دقيقتان';
    if (minutes <= 10) return `${minutes} دقائق`;
    if (minutes < 60) return `${minutes} دقيقة`;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    let result = '';
    if (hours === 1) {
        result += 'ساعة واحدة';
    } else if (hours === 2) {
        result += 'ساعتان';
    } else if (hours <= 10) {
        result += `${hours} ساعات`;
    } else {
        result += `${hours} ساعة`;
    }
    
    if (remainingMinutes > 0) {
        if (remainingMinutes === 1) {
            result += ' ودقيقة واحدة';
        } else if (remainingMinutes === 2) {
            result += ' ودقيقتان';
        } else if (remainingMinutes <= 10) {
            result += ` و${remainingMinutes} دقائق`;
        } else {
            result += ` و${remainingMinutes} دقيقة`;
        }
    }
    
    return result;
}

// Get text statistics summary
function getTextSummary(text) {
    const stats = countWords(text);
    
    let summary = [];
    
    if (stats.words > 0) {
        summary.push(`${stats.words} كلمة`);
    }
    
    if (stats.characters > 0) {
        summary.push(`${stats.characters} حرف`);
    }
    
    if (stats.sentences > 1) {
        summary.push(`${stats.sentences} جملة`);
    }
    
    if (stats.paragraphs > 1) {
        summary.push(`${stats.paragraphs} فقرة`);
    }
    
    return summary.join(' • ');
}

// Analyze text complexity
function analyzeTextComplexity(text) {
    const stats = countWords(text);
    
    let complexity = 'بسيط';
    let score = 0;
    
    // Average word length
    if (stats.averageCharactersPerWord > 6) score += 1;
    if (stats.averageCharactersPerWord > 8) score += 1;
    
    // Average sentence length
    if (stats.averageWordsPerSentence > 15) score += 1;
    if (stats.averageWordsPerSentence > 25) score += 1;
    
    // Text length
    if (stats.words > 500) score += 1;
    if (stats.words > 1000) score += 1;
    
    if (score >= 4) {
        complexity = 'معقد';
    } else if (score >= 2) {
        complexity = 'متوسط';
    }
    
    return {
        complexity,
        score,
        maxScore: 6
    };
}

// Get most frequent words
function getMostFrequentWords(text, limit = 10) {
    if (!text.trim()) return [];
    
    // Clean text and split into words
    const words = text.toLowerCase()
        .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\sa-zA-Z]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 2); // Filter out short words
    
    // Count word frequency
    const frequency = {};
    words.forEach(word => {
        frequency[word] = (frequency[word] || 0) + 1;
    });
    
    // Sort by frequency and return top words
    return Object.entries(frequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, limit)
        .map(([word, count]) => ({ word, count }));
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        countWords,
        countWordsInText,
        countSentences,
        countParagraphs,
        countLines,
        countArabicWords,
        countEnglishWords,
        countNumbers,
        countPunctuation,
        countSpaces,
        formatReadingTime,
        getTextSummary,
        analyzeTextComplexity,
        getMostFrequentWords,
        getEmptyStats
    };
}
