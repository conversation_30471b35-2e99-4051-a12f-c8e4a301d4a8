// Date Calculator JavaScript
// حساب العمر والفرق بين التواريخ

// Arabic month names
const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
];

// Hijri month names
const hijriMonths = [
    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
];

// Calculate age from birth date
function calculateAge(birthDate, currentDate = new Date()) {
    const birth = new Date(birthDate);
    const current = new Date(currentDate);
    
    if (birth > current) {
        throw new Error('تاريخ الميلاد لا يمكن أن يكون في المستقبل');
    }
    
    let years = current.getFullYear() - birth.getFullYear();
    let months = current.getMonth() - birth.getMonth();
    let days = current.getDate() - birth.getDate();
    
    // Adjust for negative days
    if (days < 0) {
        months--;
        const lastMonth = new Date(current.getFullYear(), current.getMonth(), 0);
        days += lastMonth.getDate();
    }
    
    // Adjust for negative months
    if (months < 0) {
        years--;
        months += 12;
    }
    
    // Calculate total values
    const totalDays = Math.floor((current - birth) / (1000 * 60 * 60 * 24));
    const totalWeeks = Math.floor(totalDays / 7);
    const totalMonths = years * 12 + months;
    const totalHours = totalDays * 24;
    const totalMinutes = totalHours * 60;
    
    return {
        years,
        months,
        days,
        totalDays,
        totalWeeks,
        totalMonths,
        totalHours,
        totalMinutes,
        birthDate: birth,
        currentDate: current
    };
}

// Calculate difference between two dates
function calculateDateDifference(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Ensure start date is before end date
    const earlier = start <= end ? start : end;
    const later = start <= end ? end : start;
    const isReversed = start > end;
    
    let years = later.getFullYear() - earlier.getFullYear();
    let months = later.getMonth() - earlier.getMonth();
    let days = later.getDate() - earlier.getDate();
    
    // Adjust for negative days
    if (days < 0) {
        months--;
        const lastMonth = new Date(later.getFullYear(), later.getMonth(), 0);
        days += lastMonth.getDate();
    }
    
    // Adjust for negative months
    if (months < 0) {
        years--;
        months += 12;
    }
    
    // Calculate total values
    const totalDays = Math.floor((later - earlier) / (1000 * 60 * 60 * 24));
    const totalWeeks = Math.floor(totalDays / 7);
    const totalMonths = years * 12 + months;
    const totalHours = totalDays * 24;
    const totalMinutes = totalHours * 60;
    
    return {
        years,
        months,
        days,
        totalDays,
        totalWeeks,
        totalMonths,
        totalHours,
        totalMinutes,
        startDate: earlier,
        endDate: later,
        isReversed
    };
}

// Format date in Arabic
function formatArabicDate(date, includeDay = true) {
    const day = date.getDate();
    const month = arabicMonths[date.getMonth()];
    const year = date.getFullYear();
    const dayName = getDayName(date);
    
    if (includeDay) {
        return `${dayName}، ${day} ${month} ${year}`;
    } else {
        return `${day} ${month} ${year}`;
    }
}

// Get Arabic day name
function getDayName(date) {
    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    return days[date.getDay()];
}

// Validate date input
function validateDate(day, month, year) {
    if (!day || !month || !year) {
        return { valid: false, error: 'يرجى إدخال التاريخ كاملاً' };
    }
    
    const date = new Date(year, month - 1, day);
    
    // Check if date is valid
    if (date.getDate() !== parseInt(day) || 
        date.getMonth() !== parseInt(month) - 1 || 
        date.getFullYear() !== parseInt(year)) {
        return { valid: false, error: 'التاريخ المدخل غير صحيح' };
    }
    
    // Check reasonable year range
    const currentYear = new Date().getFullYear();
    if (year < 1900 || year > currentYear + 100) {
        return { valid: false, error: 'يرجى إدخال سنة صحيحة' };
    }
    
    return { valid: true, date: date };
}

// Get days in month
function getDaysInMonth(month, year) {
    return new Date(year, month, 0).getDate();
}

// Check if year is leap year
function isLeapYear(year) {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

// Calculate next birthday
function calculateNextBirthday(birthDate) {
    const birth = new Date(birthDate);
    const today = new Date();
    const currentYear = today.getFullYear();
    
    // Create this year's birthday
    let nextBirthday = new Date(currentYear, birth.getMonth(), birth.getDate());
    
    // If birthday has passed this year, use next year
    if (nextBirthday < today) {
        nextBirthday = new Date(currentYear + 1, birth.getMonth(), birth.getDate());
    }
    
    const daysUntil = Math.ceil((nextBirthday - today) / (1000 * 60 * 60 * 24));
    
    return {
        date: nextBirthday,
        daysUntil: daysUntil,
        formattedDate: formatArabicDate(nextBirthday)
    };
}

// Calculate zodiac sign (Western)
function getZodiacSign(month, day) {
    const signs = [
        { name: 'الجدي', start: [12, 22], end: [1, 19] },
        { name: 'الدلو', start: [1, 20], end: [2, 18] },
        { name: 'الحوت', start: [2, 19], end: [3, 20] },
        { name: 'الحمل', start: [3, 21], end: [4, 19] },
        { name: 'الثور', start: [4, 20], end: [5, 20] },
        { name: 'الجوزاء', start: [5, 21], end: [6, 20] },
        { name: 'السرطان', start: [6, 21], end: [7, 22] },
        { name: 'الأسد', start: [7, 23], end: [8, 22] },
        { name: 'العذراء', start: [8, 23], end: [9, 22] },
        { name: 'الميزان', start: [9, 23], end: [10, 22] },
        { name: 'العقرب', start: [10, 23], end: [11, 21] },
        { name: 'القوس', start: [11, 22], end: [12, 21] }
    ];
    
    for (const sign of signs) {
        const [startMonth, startDay] = sign.start;
        const [endMonth, endDay] = sign.end;
        
        if ((month === startMonth && day >= startDay) || 
            (month === endMonth && day <= endDay)) {
            return sign.name;
        }
    }
    
    return 'غير محدد';
}

// Format duration in Arabic
function formatDuration(years, months, days) {
    let result = [];
    
    if (years > 0) {
        if (years === 1) {
            result.push('سنة واحدة');
        } else if (years === 2) {
            result.push('سنتان');
        } else if (years <= 10) {
            result.push(`${years} سنوات`);
        } else {
            result.push(`${years} سنة`);
        }
    }
    
    if (months > 0) {
        if (months === 1) {
            result.push('شهر واحد');
        } else if (months === 2) {
            result.push('شهران');
        } else if (months <= 10) {
            result.push(`${months} أشهر`);
        } else {
            result.push(`${months} شهراً`);
        }
    }
    
    if (days > 0) {
        if (days === 1) {
            result.push('يوم واحد');
        } else if (days === 2) {
            result.push('يومان');
        } else if (days <= 10) {
            result.push(`${days} أيام`);
        } else {
            result.push(`${days} يوماً`);
        }
    }
    
    if (result.length === 0) {
        return 'أقل من يوم';
    }
    
    if (result.length === 1) {
        return result[0];
    } else if (result.length === 2) {
        return result.join(' و ');
    } else {
        return result.slice(0, -1).join('، ') + ' و ' + result[result.length - 1];
    }
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        calculateAge,
        calculateDateDifference,
        formatArabicDate,
        getDayName,
        validateDate,
        getDaysInMonth,
        isLeapYear,
        calculateNextBirthday,
        getZodiacSign,
        formatDuration,
        arabicMonths,
        hijriMonths
    };
}
