// Unit Converter JavaScript
// تحويل وحدات القياس المختلفة

// Unit conversion data
const unitConversions = {
    length: {
        name: 'الطول',
        baseUnit: 'meter',
        units: {
            millimeter: { name: 'مليمتر', symbol: 'مم', factor: 0.001 },
            centimeter: { name: 'سنتيمتر', symbol: 'سم', factor: 0.01 },
            meter: { name: 'متر', symbol: 'م', factor: 1 },
            kilometer: { name: 'كيلومتر', symbol: 'كم', factor: 1000 },
            inch: { name: 'بوصة', symbol: 'بوصة', factor: 0.0254 },
            foot: { name: 'قدم', symbol: 'قدم', factor: 0.3048 },
            yard: { name: 'ياردة', symbol: 'ياردة', factor: 0.9144 },
            mile: { name: 'ميل', symbol: 'ميل', factor: 1609.344 }
        }
    },
    weight: {
        name: 'الوزن',
        baseUnit: 'kilogram',
        units: {
            milligram: { name: 'مليجرام', symbol: 'مجم', factor: 0.000001 },
            gram: { name: 'جرام', symbol: 'جم', factor: 0.001 },
            kilogram: { name: 'كيلوجرام', symbol: 'كجم', factor: 1 },
            ton: { name: 'طن', symbol: 'طن', factor: 1000 },
            ounce: { name: 'أونصة', symbol: 'أونصة', factor: 0.0283495 },
            pound: { name: 'رطل', symbol: 'رطل', factor: 0.453592 }
        }
    },
    volume: {
        name: 'الحجم',
        baseUnit: 'liter',
        units: {
            milliliter: { name: 'مليلتر', symbol: 'مل', factor: 0.001 },
            liter: { name: 'لتر', symbol: 'ل', factor: 1 },
            gallon_us: { name: 'جالون أمريكي', symbol: 'جالون', factor: 3.78541 },
            gallon_uk: { name: 'جالون بريطاني', symbol: 'جالون', factor: 4.54609 },
            cup: { name: 'كوب', symbol: 'كوب', factor: 0.236588 },
            pint: { name: 'باينت', symbol: 'باينت', factor: 0.473176 },
            quart: { name: 'كوارت', symbol: 'كوارت', factor: 0.946353 }
        }
    },
    temperature: {
        name: 'درجة الحرارة',
        baseUnit: 'celsius',
        units: {
            celsius: { name: 'مئوية', symbol: '°م', factor: 1 },
            fahrenheit: { name: 'فهرنهايت', symbol: '°ف', factor: 1 },
            kelvin: { name: 'كلفن', symbol: 'ك', factor: 1 }
        }
    },
    area: {
        name: 'المساحة',
        baseUnit: 'square_meter',
        units: {
            square_millimeter: { name: 'مليمتر مربع', symbol: 'مم²', factor: 0.000001 },
            square_centimeter: { name: 'سنتيمتر مربع', symbol: 'سم²', factor: 0.0001 },
            square_meter: { name: 'متر مربع', symbol: 'م²', factor: 1 },
            square_kilometer: { name: 'كيلومتر مربع', symbol: 'كم²', factor: 1000000 },
            hectare: { name: 'هكتار', symbol: 'هكتار', factor: 10000 },
            acre: { name: 'فدان', symbol: 'فدان', factor: 4046.86 },
            square_foot: { name: 'قدم مربع', symbol: 'قدم²', factor: 0.092903 },
            square_inch: { name: 'بوصة مربعة', symbol: 'بوصة²', factor: 0.00064516 }
        }
    },
    speed: {
        name: 'السرعة',
        baseUnit: 'meter_per_second',
        units: {
            meter_per_second: { name: 'متر/ثانية', symbol: 'م/ث', factor: 1 },
            kilometer_per_hour: { name: 'كيلومتر/ساعة', symbol: 'كم/س', factor: 0.277778 },
            mile_per_hour: { name: 'ميل/ساعة', symbol: 'ميل/س', factor: 0.44704 },
            knot: { name: 'عقدة', symbol: 'عقدة', factor: 0.514444 }
        }
    }
};

// Convert between units
function convertUnits(value, fromUnit, toUnit, category) {
    if (!unitConversions[category]) {
        throw new Error('فئة الوحدة غير موجودة');
    }

    const categoryData = unitConversions[category];
    const fromUnitData = categoryData.units[fromUnit];
    const toUnitData = categoryData.units[toUnit];

    if (!fromUnitData || !toUnitData) {
        throw new Error('وحدة القياس غير موجودة');
    }

    // Special handling for temperature
    if (category === 'temperature') {
        return convertTemperature(value, fromUnit, toUnit);
    }

    // Convert to base unit first, then to target unit
    const baseValue = value * fromUnitData.factor;
    const result = baseValue / toUnitData.factor;

    return result;
}

// Special temperature conversion function
function convertTemperature(value, fromUnit, toUnit) {
    let celsius;

    // Convert to Celsius first
    switch (fromUnit) {
        case 'celsius':
            celsius = value;
            break;
        case 'fahrenheit':
            celsius = (value - 32) * 5/9;
            break;
        case 'kelvin':
            celsius = value - 273.15;
            break;
        default:
            throw new Error('وحدة درجة الحرارة غير مدعومة');
    }

    // Convert from Celsius to target unit
    switch (toUnit) {
        case 'celsius':
            return celsius;
        case 'fahrenheit':
            return celsius * 9/5 + 32;
        case 'kelvin':
            return celsius + 273.15;
        default:
            throw new Error('وحدة درجة الحرارة غير مدعومة');
    }
}

// Format result with appropriate precision
function formatResult(value, precision = 6) {
    if (Math.abs(value) < 0.000001) {
        return '0';
    }
    
    // Remove trailing zeros
    const formatted = parseFloat(value.toPrecision(precision));
    return formatted.toLocaleString('ar-SA');
}

// Get unit display name
function getUnitDisplayName(unitKey, category) {
    const categoryData = unitConversions[category];
    if (!categoryData || !categoryData.units[unitKey]) {
        return unitKey;
    }
    
    const unit = categoryData.units[unitKey];
    return `${unit.name} (${unit.symbol})`;
}

// Get all units for a category
function getUnitsForCategory(category) {
    if (!unitConversions[category]) {
        return {};
    }
    
    return unitConversions[category].units;
}

// Get all categories
function getAllCategories() {
    return Object.keys(unitConversions).map(key => ({
        key: key,
        name: unitConversions[key].name
    }));
}

// Validate conversion input
function validateConversionInput(value, fromUnit, toUnit, category) {
    if (isNaN(value) || value === '') {
        return { valid: false, error: 'يرجى إدخال قيمة رقمية صحيحة' };
    }

    if (!unitConversions[category]) {
        return { valid: false, error: 'فئة الوحدة غير موجودة' };
    }

    const categoryData = unitConversions[category];
    if (!categoryData.units[fromUnit] || !categoryData.units[toUnit]) {
        return { valid: false, error: 'وحدة القياس غير موجودة' };
    }

    // Special validation for temperature
    if (category === 'temperature') {
        if (fromUnit === 'kelvin' && value < 0) {
            return { valid: false, error: 'درجة الحرارة بالكلفن لا يمكن أن تكون أقل من الصفر' };
        }
        if (fromUnit === 'celsius' && value < -273.15) {
            return { valid: false, error: 'درجة الحرارة بالمئوية لا يمكن أن تكون أقل من -273.15' };
        }
        if (fromUnit === 'fahrenheit' && value < -459.67) {
            return { valid: false, error: 'درجة الحرارة بالفهرنهايت لا يمكن أن تكون أقل من -459.67' };
        }
    }

    return { valid: true };
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        unitConversions,
        convertUnits,
        formatResult,
        getUnitDisplayName,
        getUnitsForCategory,
        getAllCategories,
        validateConversionInput
    };
}
