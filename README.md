# أدوات مفيدة - مجموعة شاملة من الأدوات المجانية

موقع إلكتروني متجاوب وجذاب يحتوي على مجموعة شاملة من الأدوات المفيدة والمجانية باللغة العربية.

## 🚀 الأدوات المتاحة

### 💰 الأدوات المالية
- **تحويل العملات**: تحويل بين العملات المختلفة بأسعار حقيقية ومحدثة
- **تفقيط الأرقام**: تحويل الأرقام إلى كلمات باللغة العربية بطريقة محاسبية ولغوية صحيحة

### 📏 أدوات القياس
- **تحويل وحدات القياس**: تحويل بين وحدات الطول والوزن والحجم ودرجة الحرارة والمساحة والسرعة

### 📅 أدوات التاريخ
- **حساب العمر**: احسب عمرك بالسنوات والشهور والأيام بدقة
- **الفرق بين التواريخ**: احسب الفرق بين تاريخين بالسنوات والشهور والأيام

### 📝 أدوات النصوص
- **عد الكلمات والأحرف**: عد الكلمات والأحرف والفقرات في النصوص العربية والإنجليزية
- **الكتابة على الصور**: أضف نصوص مخصصة على الصور مع خيارات تنسيق متنوعة

### 🎮 الألعاب
- **لعبة إكس أوه**: العب لعبة إكس أوه مع الأصدقاء محلياً أو ضد الكمبيوتر

## 🛠️ التقنيات المستخدمة

- **HTML5**: للهيكل الأساسي
- **CSS3**: للتصميم والأنماط
- **Bootstrap 5**: للتصميم المتجاوب
- **JavaScript (ES6+)**: للوظائف التفاعلية
- **Font Awesome**: للأيقونات
- **Google Fonts**: للخطوط العربية (Cairo)
- **Canvas API**: للكتابة على الصور
- **Exchange Rate API**: لأسعار العملات الحقيقية

## 📁 هيكل المشروع

```
├── index.html                 # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css         # ملف الأنماط المخصص
│   └── js/
│       ├── main.js           # الملف الرئيسي للجافا سكريبت
│       ├── number-to-words.js # تفقيط الأرقام
│       ├── unit-converter.js  # تحويل وحدات القياس
│       ├── date-calculator.js # حساب التواريخ والعمر
│       ├── word-counter.js    # عداد الكلمات والأحرف
│       ├── text-on-image.js   # الكتابة على الصور
│       └── tic-tac-toe.js     # لعبة إكس أوه
└── README.md                  # ملف التوثيق
```

## 🚀 كيفية الاستخدام

1. **تشغيل الموقع محلياً**:
   - افتح ملف `index.html` في المتصفح مباشرة
   - أو استخدم خادم محلي مثل Live Server في VS Code

2. **رفع الموقع على الإنترنت**:
   - ارفع جميع الملفات إلى خادم الويب
   - تأكد من الحفاظ على هيكل المجلدات

## ✨ المميزات

- **تصميم متجاوب**: يعمل على جميع الأجهزة (الهاتف، التابلت، الكمبيوتر)
- **واجهة عربية**: مصمم خصيصاً للمستخدمين العرب
- **سهولة الاستخدام**: واجهة بديهية وسهلة التنقل
- **أدوات متنوعة**: مجموعة شاملة من الأدوات المفيدة
- **مجاني بالكامل**: جميع الأدوات مجانية للاستخدام
- **لا يتطلب تسجيل**: استخدم الأدوات مباشرة بدون تسجيل
- **يعمل بدون إنترنت**: معظم الأدوات تعمل محلياً (عدا تحويل العملات)

## 🔧 التخصيص

### إضافة أداة جديدة

1. أنشئ ملف JavaScript جديد في مجلد `assets/js/`
2. أضف الأداة إلى قائمة الأدوات في `index.html`
3. أضف دالة التحميل في `main.js`
4. أضف الأنماط المطلوبة في `style.css`

### تخصيص الألوان

عدّل المتغيرات في بداية ملف `style.css`:

```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    /* ... باقي المتغيرات */
}
```

### إضافة إعلانات AdSense

أضف كود الإعلانات في الأماكن المناسبة:
- بين الأدوات في الصفحة الرئيسية
- في أسفل كل أداة
- في الشريط الجانبي (إذا أُضيف)

## 📱 التوافق

- **المتصفحات**: Chrome, Firefox, Safari, Edge (الإصدارات الحديثة)
- **الأجهزة**: الهاتف المحمول, التابلت, الكمبيوتر
- **أنظمة التشغيل**: Windows, macOS, Linux, iOS, Android

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة أدوات جديدة
- تحسين الأدوات الموجودة
- إصلاح الأخطاء
- تحسين التصميم
- ترجمة إلى لغات أخرى

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام المجاني.

## 📞 التواصل

إذا كان لديك أي استفسارات أو اقتراحات، لا تتردد في التواصل معنا.

---

**ملاحظة**: تأكد من الحصول على مفتاح API مجاني من [ExchangeRate-API](https://exchangerate-api.com/) لتفعيل أداة تحويل العملات بالأسعار الحقيقية.
