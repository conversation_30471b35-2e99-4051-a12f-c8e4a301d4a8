# دليل التشغيل والإعداد - أدوات مفيدة

## 🚀 التشغيل السريع

### 1. التشغيل المحلي
```bash
# افتح ملف index.html في المتصفح مباشرة
# أو استخدم خادم محلي
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### 2. باستخدام Live Server (VS Code)
1. افتح المشروع في VS Code
2. انقر بالزر الأيمن على `index.html`
3. اختر "Open with Live Server"

## ⚙️ الإعداد المتقدم

### 1. إعداد API تحويل العملات
1. احصل على مفتاح API مجاني من [ExchangeRate-API](https://exchangerate-api.com/)
2. عدّل ملف `config.js`:
```javascript
apis: {
    exchangeRate: 'your-api-key-here'
}
```

### 2. <PERSON><PERSON><PERSON><PERSON> Google AdSense
1. احصل على حساب AdSense
2. عدّل ملف `index.html` و `config.js`:
```javascript
adsense: {
    client: 'ca-pub-your-client-id',
    slots: {
        banner: 'your-banner-slot-id',
        sidebar: 'your-sidebar-slot-id'
    }
}
```

### 3. إعداد Google Analytics
1. أنشئ حساب Google Analytics 4
2. عدّل ملف `config.js`:
```javascript
analytics: {
    googleAnalytics: 'G-your-measurement-id'
}
```

## 🌐 النشر على الإنترنت

### 1. GitHub Pages
1. ارفع الملفات إلى مستودع GitHub
2. فعّل GitHub Pages من إعدادات المستودع
3. اختر المجلد الرئيسي كمصدر

### 2. Netlify
1. اسحب مجلد المشروع إلى [Netlify](https://netlify.com)
2. أو اربط مستودع GitHub
3. سيتم النشر تلقائياً

### 3. Vercel
```bash
npm i -g vercel
vercel --prod
```

### 4. خادم تقليدي
1. ارفع جميع الملفات إلى مجلد الويب
2. تأكد من الحفاظ على هيكل المجلدات
3. اضبط إعدادات الخادم للملفات الثابتة

## 🔧 التخصيص

### 1. تغيير الألوان
عدّل متغيرات CSS في `assets/css/style.css`:
```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

### 2. إضافة أداة جديدة
1. أنشئ ملف JavaScript في `assets/js/`
2. أضف الأداة إلى `index.html`
3. أضف دالة التحميل في `main.js`

### 3. تخصيص الخطوط
عدّل رابط Google Fonts في `index.html`:
```html
<link href="https://fonts.googleapis.com/css2?family=YourFont" rel="stylesheet">
```

## 📱 إعداد PWA

### 1. الأيقونات
1. أنشئ مجلد `assets/icons/`
2. أضف أيقونات بأحجام مختلفة:
   - 72x72, 96x96, 128x128, 144x144
   - 152x152, 192x192, 384x384, 512x512

### 2. Service Worker
Service Worker مُعدّ مسبقاً في `sw.js`
- يعمل بدون إنترنت
- يحفظ الملفات في الذاكرة المؤقتة
- يحدث أسعار العملات تلقائياً

## 🔍 اختبار الموقع

### 1. اختبار الأدوات
- [ ] تحويل العملات (يتطلب إنترنت)
- [ ] تفقيط الأرقام
- [ ] تحويل وحدات القياس
- [ ] حساب العمر
- [ ] الفرق بين التواريخ
- [ ] عد الكلمات والأحرف
- [ ] الكتابة على الصور
- [ ] لعبة إكس أوه

### 2. اختبار التجاوب
- [ ] الهاتف المحمول
- [ ] التابلت
- [ ] الكمبيوتر المكتبي
- [ ] الكمبيوتر المحمول

### 3. اختبار المتصفحات
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## 🛠️ استكشاف الأخطاء

### 1. أداة تحويل العملات لا تعمل
- تأكد من وجود اتصال بالإنترنت
- تحقق من صحة مفتاح API
- افحص وحدة التحكم للأخطاء

### 2. الخطوط لا تظهر بشكل صحيح
- تأكد من تحميل خط Cairo
- تحقق من اتصال الإنترنت
- امسح ذاكرة التخزين المؤقت

### 3. الأدوات لا تعمل
- افحص وحدة التحكم للأخطاء
- تأكد من تحميل جميع ملفات JavaScript
- تحقق من ترتيب تحميل الملفات

## 📊 مراقبة الأداء

### 1. أدوات القياس
- Google PageSpeed Insights
- GTmetrix
- WebPageTest
- Lighthouse

### 2. مؤشرات الأداء
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Cumulative Layout Shift (CLS)
- First Input Delay (FID)

## 🔒 الأمان

### 1. HTTPS
- استخدم HTTPS دائماً في الإنتاج
- معظم خدمات الاستضافة توفر SSL مجاني

### 2. Content Security Policy
أضف إلى `<head>`:
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https:; style-src 'self' 'unsafe-inline' https:;">
```

## 📈 تحسين SEO

### 1. Meta Tags
- تأكد من وجود title و description
- أضف Open Graph tags
- استخدم structured data

### 2. Sitemap
أنشئ ملف `sitemap.xml`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://your-domain.com/</loc>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
</urlset>
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md
2. افحص وحدة التحكم للأخطاء
3. تأكد من اتباع جميع خطوات الإعداد
4. تحقق من توافق المتصفح

---

**ملاحظة**: هذا المشروع مصمم ليعمل بدون خادم خلفي، جميع العمليات تتم في المتصفح عدا تحويل العملات الذي يتطلب API خارجي.
