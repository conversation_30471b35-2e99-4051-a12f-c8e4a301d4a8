// Tic <PERSON><PERSON>e Game JavaScript
// لعبة إكس أوه

// Game state
let gameState = {
    board: Array(9).fill(''),
    currentPlayer: 'X',
    gameMode: 'local', // 'local', 'ai', 'online'
    gameActive: true,
    scores: { X: 0, O: 0, draws: 0 },
    playerNames: { X: 'اللاعب الأول', O: 'اللاعب الثاني' }
};

// Winning combinations
const winningCombinations = [
    [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
    [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
    [0, 4, 8], [2, 4, 6] // Diagonals
];

// Initialize game
function initTicTacToe() {
    resetGame();
    updateDisplay();
}

// Reset game
function resetGame() {
    gameState.board = Array(9).fill('');
    gameState.currentPlayer = 'X';
    gameState.gameActive = true;
    updateDisplay();
}

// Make a move
function makeMove(cellIndex) {
    if (!gameState.gameActive || gameState.board[cellIndex] !== '') {
        return false;
    }
    
    gameState.board[cellIndex] = gameState.currentPlayer;
    
    // Check for win or draw
    const result = checkGameResult();
    
    if (result.gameOver) {
        gameState.gameActive = false;
        handleGameEnd(result);
    } else {
        // Switch player
        gameState.currentPlayer = gameState.currentPlayer === 'X' ? 'O' : 'X';
        
        // AI move if playing against computer
        if (gameState.gameMode === 'ai' && gameState.currentPlayer === 'O' && gameState.gameActive) {
            setTimeout(() => {
                makeAIMove();
            }, 500);
        }
    }
    
    updateDisplay();
    return true;
}

// Check game result
function checkGameResult() {
    // Check for win
    for (const combination of winningCombinations) {
        const [a, b, c] = combination;
        if (gameState.board[a] && 
            gameState.board[a] === gameState.board[b] && 
            gameState.board[a] === gameState.board[c]) {
            return {
                gameOver: true,
                winner: gameState.board[a],
                winningCombination: combination
            };
        }
    }
    
    // Check for draw
    if (!gameState.board.includes('')) {
        return {
            gameOver: true,
            winner: null,
            isDraw: true
        };
    }
    
    return { gameOver: false };
}

// Handle game end
function handleGameEnd(result) {
    if (result.isDraw) {
        gameState.scores.draws++;
        showGameMessage('تعادل!', 'warning');
    } else {
        gameState.scores[result.winner]++;
        const winnerName = gameState.playerNames[result.winner];
        showGameMessage(`${winnerName} فاز!`, 'success');
        
        // Highlight winning combination
        if (result.winningCombination) {
            highlightWinningCells(result.winningCombination);
        }
    }
    
    updateScoreboard();
}

// AI Move (Simple algorithm)
function makeAIMove() {
    if (!gameState.gameActive) return;
    
    let bestMove = getBestMove();
    
    if (bestMove !== -1) {
        makeMove(bestMove);
    }
}

// Get best move for AI
function getBestMove() {
    // Try to win
    for (let i = 0; i < 9; i++) {
        if (gameState.board[i] === '') {
            gameState.board[i] = 'O';
            if (checkGameResult().winner === 'O') {
                gameState.board[i] = '';
                return i;
            }
            gameState.board[i] = '';
        }
    }
    
    // Try to block player from winning
    for (let i = 0; i < 9; i++) {
        if (gameState.board[i] === '') {
            gameState.board[i] = 'X';
            if (checkGameResult().winner === 'X') {
                gameState.board[i] = '';
                return i;
            }
            gameState.board[i] = '';
        }
    }
    
    // Take center if available
    if (gameState.board[4] === '') {
        return 4;
    }
    
    // Take corners
    const corners = [0, 2, 6, 8];
    const availableCorners = corners.filter(i => gameState.board[i] === '');
    if (availableCorners.length > 0) {
        return availableCorners[Math.floor(Math.random() * availableCorners.length)];
    }
    
    // Take any available cell
    const availableCells = gameState.board
        .map((cell, index) => cell === '' ? index : null)
        .filter(index => index !== null);
    
    if (availableCells.length > 0) {
        return availableCells[Math.floor(Math.random() * availableCells.length)];
    }
    
    return -1;
}

// Update display
function updateDisplay() {
    updateBoard();
    updateCurrentPlayerDisplay();
    updateGameStatus();
}

// Update board display
function updateBoard() {
    const cells = document.querySelectorAll('.tic-tac-toe-cell');
    cells.forEach((cell, index) => {
        cell.textContent = gameState.board[index];
        cell.className = 'tic-tac-toe-cell';
        
        if (gameState.board[index] === 'X') {
            cell.classList.add('player-x');
        } else if (gameState.board[index] === 'O') {
            cell.classList.add('player-o');
        }
        
        if (!gameState.gameActive) {
            cell.classList.add('disabled');
        }
    });
}

// Update current player display
function updateCurrentPlayerDisplay() {
    const currentPlayerElement = document.getElementById('currentPlayer');
    if (currentPlayerElement) {
        if (gameState.gameActive) {
            const playerName = gameState.playerNames[gameState.currentPlayer];
            currentPlayerElement.textContent = `دور: ${playerName} (${gameState.currentPlayer})`;
        } else {
            currentPlayerElement.textContent = 'انتهت اللعبة';
        }
    }
}

// Update game status
function updateGameStatus() {
    const statusElement = document.getElementById('gameStatus');
    if (statusElement) {
        if (gameState.gameActive) {
            statusElement.textContent = 'اللعبة جارية...';
            statusElement.className = 'alert alert-info';
        } else {
            // Status will be updated by handleGameEnd
        }
    }
}

// Update scoreboard
function updateScoreboard() {
    const scoreX = document.getElementById('scoreX');
    const scoreO = document.getElementById('scoreO');
    const scoreDraws = document.getElementById('scoreDraws');
    
    if (scoreX) scoreX.textContent = gameState.scores.X;
    if (scoreO) scoreO.textContent = gameState.scores.O;
    if (scoreDraws) scoreDraws.textContent = gameState.scores.draws;
}

// Show game message
function showGameMessage(message, type = 'info') {
    const statusElement = document.getElementById('gameStatus');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `alert alert-${type}`;
    }
}

// Highlight winning cells
function highlightWinningCells(combination) {
    const cells = document.querySelectorAll('.tic-tac-toe-cell');
    combination.forEach(index => {
        cells[index].classList.add('winning-cell');
    });
}

// Set game mode
function setGameMode(mode) {
    gameState.gameMode = mode;
    
    if (mode === 'ai') {
        gameState.playerNames.O = 'الكمبيوتر';
    } else {
        gameState.playerNames.O = 'اللاعب الثاني';
    }
    
    resetGame();
}

// Set player names
function setPlayerNames(playerX, playerO) {
    gameState.playerNames.X = playerX || 'اللاعب الأول';
    gameState.playerNames.O = playerO || 'اللاعب الثاني';
    updateDisplay();
}

// Reset scores
function resetScores() {
    gameState.scores = { X: 0, O: 0, draws: 0 };
    updateScoreboard();
}

// Get game statistics
function getGameStats() {
    const totalGames = gameState.scores.X + gameState.scores.O + gameState.scores.draws;
    
    return {
        totalGames,
        xWins: gameState.scores.X,
        oWins: gameState.scores.O,
        draws: gameState.scores.draws,
        xWinRate: totalGames > 0 ? Math.round((gameState.scores.X / totalGames) * 100) : 0,
        oWinRate: totalGames > 0 ? Math.round((gameState.scores.O / totalGames) * 100) : 0,
        drawRate: totalGames > 0 ? Math.round((gameState.scores.draws / totalGames) * 100) : 0
    };
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initTicTacToe,
        resetGame,
        makeMove,
        setGameMode,
        setPlayerNames,
        resetScores,
        getGameStats,
        gameState
    };
}
