// Arabic Number to Words Converter
// تحويل الأرقام إلى كلمات باللغة العربية

// Arabic number names
const arabicNumbers = {
    ones: [
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
        'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ],
    onesF: [
        '', 'واحدة', 'اثنتان', 'ثلاث', 'أربع', 'خمس', 'ست', 'سبع', 'ثمان', 'تسع',
        'عشر', 'إحدى عشرة', 'اثنتا عشرة', 'ثلاث عشرة', 'أربع عشرة', 'خمس عشرة',
        'ست عشرة', 'سبع عشرة', 'ثمان عشرة', 'تسع عشرة'
    ],
    tens: [
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ],
    hundreds: [
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    ],
    scale: [
        { name: '', nameF: '' },
        { name: 'ألف', nameF: 'ألف' },
        { name: 'مليون', nameF: 'مليون' },
        { name: 'مليار', nameF: 'مليار' },
        { name: 'تريليون', nameF: 'تريليون' }
    ]
};

// Currency names
const currencies = {
    SAR: { main: 'ريال', sub: 'هللة', mainF: 'ريال', subF: 'هللة' },
    USD: { main: 'دولار', sub: 'سنت', mainF: 'دولار', subF: 'سنت' },
    EUR: { main: 'يورو', sub: 'سنت', mainF: 'يورو', subF: 'سنت' },
    EGP: { main: 'جنيه', sub: 'قرش', mainF: 'جنيه', subF: 'قرش' },
    AED: { main: 'درهم', sub: 'فلس', mainF: 'درهم', subF: 'فلس' },
    JOD: { main: 'دينار', sub: 'فلس', mainF: 'دينار', subF: 'فلس' },
    KWD: { main: 'دينار', sub: 'فلس', mainF: 'دينار', subF: 'فلس' }
};

// Main function to convert number to Arabic words
function numberToArabicWords(number, currencyCode = 'NONE') {
    if (number === 0) {
        return currencyCode === 'NONE' ? 'صفر' : `صفر ${currencies[currencyCode]?.main || ''}`;
    }

    const parts = number.toString().split('.');
    const integerPart = parseInt(parts[0]);
    const decimalPart = parts[1] ? parseInt(parts[1].padEnd(2, '0').substring(0, 2)) : 0;

    let result = '';

    // Convert integer part
    if (integerPart > 0) {
        const integerWords = convertIntegerToWords(integerPart);
        if (currencyCode !== 'NONE' && currencies[currencyCode]) {
            const currency = currencies[currencyCode];
            result += integerWords + ' ' + (integerPart === 1 ? currency.main : currency.main);
        } else {
            result += integerWords;
        }
    }

    // Convert decimal part
    if (decimalPart > 0 && currencyCode !== 'NONE' && currencies[currencyCode]) {
        const decimalWords = convertIntegerToWords(decimalPart);
        const currency = currencies[currencyCode];
        if (result) result += ' و ';
        result += decimalWords + ' ' + (decimalPart === 1 ? currency.sub : currency.sub);
    }

    return result || 'صفر';
}

// Convert integer to Arabic words
function convertIntegerToWords(number) {
    if (number === 0) return '';
    if (number < 0) return 'سالب ' + convertIntegerToWords(-number);

    let result = '';
    let scaleIndex = 0;

    while (number > 0) {
        const chunk = number % 1000;
        if (chunk !== 0) {
            const chunkWords = convertChunkToWords(chunk, scaleIndex > 0);
            const scale = arabicNumbers.scale[scaleIndex];
            
            if (scaleIndex > 0) {
                if (chunk === 1) {
                    result = scale.name + (result ? ' ' + result : '');
                } else if (chunk === 2) {
                    result = scale.name + 'ان' + (result ? ' ' + result : '');
                } else if (chunk <= 10) {
                    result = chunkWords + ' ' + scale.name + (result ? ' ' + result : '');
                } else {
                    result = chunkWords + ' ' + scale.name + (result ? ' ' + result : '');
                }
            } else {
                result = chunkWords + (result ? ' ' + result : '');
            }
        }
        
        number = Math.floor(number / 1000);
        scaleIndex++;
    }

    return result.trim();
}

// Convert a chunk (1-999) to Arabic words
function convertChunkToWords(number, isFeminine = false) {
    if (number === 0) return '';

    let result = '';
    const ones = isFeminine ? arabicNumbers.onesF : arabicNumbers.ones;

    // Hundreds
    const hundreds = Math.floor(number / 100);
    if (hundreds > 0) {
        if (hundreds === 1) {
            result += 'مائة';
        } else if (hundreds === 2) {
            result += 'مائتان';
        } else {
            result += arabicNumbers.hundreds[hundreds];
        }
    }

    // Tens and ones
    const remainder = number % 100;
    if (remainder > 0) {
        if (result) result += ' ';
        
        if (remainder < 20) {
            result += ones[remainder];
        } else {
            const tens = Math.floor(remainder / 10);
            const onesDigit = remainder % 10;
            
            if (onesDigit > 0) {
                result += ones[onesDigit] + ' و ' + arabicNumbers.tens[tens];
            } else {
                result += arabicNumbers.tens[tens];
            }
        }
    }

    return result;
}

// Format number with Arabic numerals
function formatArabicNumber(number) {
    return number.toLocaleString('ar-SA');
}

// Validate number input
function validateNumberInput(input) {
    const number = parseFloat(input);
    
    if (isNaN(number)) {
        return { valid: false, error: 'يرجى إدخال رقم صحيح' };
    }
    
    if (number < 0) {
        return { valid: false, error: 'لا يمكن تفقيط الأرقام السالبة' };
    }
    
    if (number > 999999999999) {
        return { valid: false, error: 'الرقم كبير جداً. الحد الأقصى هو 999,999,999,999' };
    }
    
    return { valid: true, number: number };
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        numberToArabicWords,
        formatArabicNumber,
        validateNumberInput
    };
}
