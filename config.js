// Website Configuration
// إعدادات الموقع

const CONFIG = {
    // Site Information
    site: {
        name: 'أدوات مفيدة',
        description: 'مجموعة شاملة من الأدوات المجانية والمفيدة',
        url: 'https://your-domain.com',
        language: 'ar',
        direction: 'rtl'
    },
    
    // API Keys
    apis: {
        exchangeRate: 'your-exchange-rate-api-key',
        googleMaps: 'your-google-maps-api-key' // للاستخدام المستقبلي
    },
    
    // AdSense Configuration
    adsense: {
        client: 'ca-pub-XXXXXXXXXX', // Replace with your AdSense client ID
        slots: {
            banner: 'XXXXXXXXXX',     // Banner ad slot
            sidebar: 'XXXXXXXXXX',    // Sidebar ad slot
            footer: 'XXXXXXXXXX'      // Footer ad slot
        },
        enabled: true
    },
    
    // Analytics
    analytics: {
        googleAnalytics: 'G-XXXXXXXXXX', // Replace with your GA4 ID
        enabled: true
    },
    
    // Features
    features: {
        darkMode: false,           // Enable dark mode toggle
        multiLanguage: false,      // Enable multiple languages
        offlineMode: true,         // Enable offline functionality
        printMode: true            // Enable print-friendly styles
    },
    
    // Tool Settings
    tools: {
        currencyConverter: {
            enabled: true,
            defaultFromCurrency: 'USD',
            defaultToCurrency: 'SAR',
            updateInterval: 3600000 // 1 hour in milliseconds
        },
        
        numberToWords: {
            enabled: true,
            defaultCurrency: 'SAR',
            maxNumber: 999999999999
        },
        
        unitConverter: {
            enabled: true,
            defaultCategory: 'length',
            precision: 6
        },
        
        ageCalculator: {
            enabled: true,
            minYear: 1900,
            maxYear: new Date().getFullYear() + 100
        },
        
        dateDifference: {
            enabled: true,
            minYear: 1900,
            maxYear: new Date().getFullYear() + 100
        },
        
        wordCounter: {
            enabled: true,
            realTimeUpdate: true,
            showDetailedStats: true
        },
        
        textOnImage: {
            enabled: true,
            maxImageSize: 5 * 1024 * 1024, // 5MB
            supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            defaultFont: 'Cairo, Arial, sans-serif'
        },
        
        ticTacToe: {
            enabled: true,
            defaultMode: 'ai', // 'local' or 'ai'
            aiDifficulty: 'medium' // 'easy', 'medium', 'hard'
        }
    },
    
    // UI Settings
    ui: {
        theme: {
            primaryColor: '#2563eb',
            secondaryColor: '#1e40af',
            accentColor: '#f59e0b',
            successColor: '#10b981',
            dangerColor: '#ef4444',
            warningColor: '#f59e0b',
            infoColor: '#3b82f6'
        },
        
        animations: {
            enabled: true,
            duration: 300 // milliseconds
        },
        
        layout: {
            containerMaxWidth: '1200px',
            sidebarWidth: '300px',
            headerHeight: '70px'
        }
    },
    
    // SEO Settings
    seo: {
        keywords: [
            'أدوات مفيدة',
            'تحويل العملات',
            'تفقيط الأرقام',
            'حساب العمر',
            'عد الكلمات',
            'أدوات مجانية',
            'أدوات عربية'
        ],
        
        socialMedia: {
            twitter: '@your-twitter',
            facebook: 'your-facebook-page',
            instagram: '@your-instagram'
        }
    },
    
    // Performance Settings
    performance: {
        lazyLoading: true,
        imageOptimization: true,
        caching: {
            enabled: true,
            duration: 24 * 60 * 60 * 1000 // 24 hours
        }
    },
    
    // Contact Information
    contact: {
        email: '<EMAIL>',
        phone: '+966-XX-XXX-XXXX',
        address: 'المملكة العربية السعودية'
    },
    
    // Legal
    legal: {
        privacyPolicy: '/privacy-policy.html',
        termsOfService: '/terms-of-service.html',
        copyright: '© 2024 أدوات مفيدة. جميع الحقوق محفوظة.'
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
}
