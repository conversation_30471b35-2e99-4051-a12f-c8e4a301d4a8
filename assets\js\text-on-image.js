// Text on Image JavaScript
// أداة الكتابة على الصور

// Global variables for text on image
let canvas, ctx, originalImage, textElements = [];
let selectedElement = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };

// Initialize text on image tool
function initTextOnImage() {
    canvas = document.getElementById('imageCanvas');
    ctx = canvas.getContext('2d');
    
    // Set up event listeners
    setupCanvasEvents();
}

// Setup canvas event listeners
function setupCanvasEvents() {
    if (!canvas) return;
    
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('click', handleCanvasClick);
    
    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouchStart);
    canvas.addEventListener('touchmove', handleTouchMove);
    canvas.addEventListener('touchend', handleTouchEnd);
}

// Load image onto canvas
function loadImageOnCanvas(file) {
    return new Promise((resolve, reject) => {
        if (!file || !file.type.startsWith('image/')) {
            reject(new Error('يرجى اختيار ملف صورة صحيح'));
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = new Image();
            img.onload = function() {
                originalImage = img;
                
                // Calculate canvas size maintaining aspect ratio
                const maxWidth = 800;
                const maxHeight = 600;
                let { width, height } = calculateCanvasSize(img.width, img.height, maxWidth, maxHeight);
                
                canvas.width = width;
                canvas.height = height;
                
                // Draw image on canvas
                ctx.drawImage(img, 0, 0, width, height);
                
                // Clear existing text elements
                textElements = [];
                selectedElement = null;
                
                resolve({ width, height });
            };
            
            img.onerror = function() {
                reject(new Error('فشل في تحميل الصورة'));
            };
            
            img.src = e.target.result;
        };
        
        reader.onerror = function() {
            reject(new Error('فشل في قراءة الملف'));
        };
        
        reader.readAsDataURL(file);
    });
}

// Calculate canvas size maintaining aspect ratio
function calculateCanvasSize(imgWidth, imgHeight, maxWidth, maxHeight) {
    let width = imgWidth;
    let height = imgHeight;
    
    // Scale down if image is too large
    if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
    }
    
    if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
    }
    
    return { width: Math.round(width), height: Math.round(height) };
}

// Add text element to canvas
function addTextElement(text, options = {}) {
    const defaultOptions = {
        x: canvas.width / 2,
        y: canvas.height / 2,
        fontSize: 24,
        fontFamily: 'Cairo, Arial, sans-serif',
        color: '#000000',
        backgroundColor: 'transparent',
        textAlign: 'center',
        fontWeight: 'normal',
        fontStyle: 'normal',
        textDecoration: 'none',
        opacity: 1,
        rotation: 0,
        shadow: false,
        shadowColor: '#000000',
        shadowBlur: 5,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
        border: false,
        borderColor: '#000000',
        borderWidth: 1,
        padding: 10
    };
    
    const textElement = { ...defaultOptions, ...options, text, id: Date.now() };
    textElements.push(textElement);
    
    redrawCanvas();
    return textElement;
}

// Redraw canvas with image and all text elements
function redrawCanvas() {
    if (!originalImage) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw original image
    ctx.drawImage(originalImage, 0, 0, canvas.width, canvas.height);
    
    // Draw all text elements
    textElements.forEach(element => {
        drawTextElement(element);
    });
    
    // Highlight selected element
    if (selectedElement) {
        highlightSelectedElement(selectedElement);
    }
}

// Draw individual text element
function drawTextElement(element) {
    ctx.save();
    
    // Set global alpha for opacity
    ctx.globalAlpha = element.opacity;
    
    // Move to element position
    ctx.translate(element.x, element.y);
    
    // Apply rotation
    if (element.rotation !== 0) {
        ctx.rotate((element.rotation * Math.PI) / 180);
    }
    
    // Set font properties
    const fontStyle = element.fontStyle !== 'normal' ? element.fontStyle + ' ' : '';
    const fontWeight = element.fontWeight !== 'normal' ? element.fontWeight + ' ' : '';
    ctx.font = `${fontStyle}${fontWeight}${element.fontSize}px ${element.fontFamily}`;
    ctx.textAlign = element.textAlign;
    ctx.textBaseline = 'middle';
    
    // Measure text
    const textMetrics = ctx.measureText(element.text);
    const textWidth = textMetrics.width;
    const textHeight = element.fontSize;
    
    // Draw background if specified
    if (element.backgroundColor !== 'transparent') {
        ctx.fillStyle = element.backgroundColor;
        const bgX = element.textAlign === 'center' ? -textWidth / 2 - element.padding : 
                   element.textAlign === 'right' ? -textWidth - element.padding : -element.padding;
        const bgY = -textHeight / 2 - element.padding;
        const bgWidth = textWidth + element.padding * 2;
        const bgHeight = textHeight + element.padding * 2;
        
        ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
    }
    
    // Draw border if specified
    if (element.border) {
        ctx.strokeStyle = element.borderColor;
        ctx.lineWidth = element.borderWidth;
        const borderX = element.textAlign === 'center' ? -textWidth / 2 - element.padding : 
                       element.textAlign === 'right' ? -textWidth - element.padding : -element.padding;
        const borderY = -textHeight / 2 - element.padding;
        const borderWidth = textWidth + element.padding * 2;
        const borderHeight = textHeight + element.padding * 2;
        
        ctx.strokeRect(borderX, borderY, borderWidth, borderHeight);
    }
    
    // Draw shadow if specified
    if (element.shadow) {
        ctx.shadowColor = element.shadowColor;
        ctx.shadowBlur = element.shadowBlur;
        ctx.shadowOffsetX = element.shadowOffsetX;
        ctx.shadowOffsetY = element.shadowOffsetY;
    }
    
    // Draw text
    ctx.fillStyle = element.color;
    ctx.fillText(element.text, 0, 0);
    
    // Draw text decoration
    if (element.textDecoration === 'underline') {
        ctx.beginPath();
        ctx.moveTo(-textWidth / 2, textHeight / 4);
        ctx.lineTo(textWidth / 2, textHeight / 4);
        ctx.strokeStyle = element.color;
        ctx.lineWidth = 1;
        ctx.stroke();
    } else if (element.textDecoration === 'line-through') {
        ctx.beginPath();
        ctx.moveTo(-textWidth / 2, 0);
        ctx.lineTo(textWidth / 2, 0);
        ctx.strokeStyle = element.color;
        ctx.lineWidth = 1;
        ctx.stroke();
    }
    
    ctx.restore();
}

// Highlight selected element
function highlightSelectedElement(element) {
    ctx.save();
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    
    // Calculate bounding box
    ctx.font = `${element.fontSize}px ${element.fontFamily}`;
    const textMetrics = ctx.measureText(element.text);
    const textWidth = textMetrics.width;
    const textHeight = element.fontSize;
    
    const x = element.x - textWidth / 2 - 5;
    const y = element.y - textHeight / 2 - 5;
    const width = textWidth + 10;
    const height = textHeight + 10;
    
    ctx.strokeRect(x, y, width, height);
    ctx.restore();
}

// Handle mouse events
function handleMouseDown(e) {
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const clickedElement = getElementAtPosition(x, y);
    if (clickedElement) {
        selectedElement = clickedElement;
        isDragging = true;
        dragOffset.x = x - clickedElement.x;
        dragOffset.y = y - clickedElement.y;
        redrawCanvas();
    } else {
        selectedElement = null;
        redrawCanvas();
    }
}

function handleMouseMove(e) {
    if (isDragging && selectedElement) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        selectedElement.x = x - dragOffset.x;
        selectedElement.y = y - dragOffset.y;
        
        redrawCanvas();
    }
}

function handleMouseUp(e) {
    isDragging = false;
}

function handleCanvasClick(e) {
    // Handle click events if needed
}

// Touch event handlers
function handleTouchStart(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function handleTouchMove(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function handleTouchEnd(e) {
    e.preventDefault();
    const mouseEvent = new MouseEvent('mouseup', {});
    canvas.dispatchEvent(mouseEvent);
}

// Get element at position
function getElementAtPosition(x, y) {
    // Check elements in reverse order (top to bottom)
    for (let i = textElements.length - 1; i >= 0; i--) {
        const element = textElements[i];
        
        // Simple bounding box check
        ctx.font = `${element.fontSize}px ${element.fontFamily}`;
        const textMetrics = ctx.measureText(element.text);
        const textWidth = textMetrics.width;
        const textHeight = element.fontSize;
        
        const left = element.x - textWidth / 2;
        const right = element.x + textWidth / 2;
        const top = element.y - textHeight / 2;
        const bottom = element.y + textHeight / 2;
        
        if (x >= left && x <= right && y >= top && y <= bottom) {
            return element;
        }
    }
    
    return null;
}

// Update selected element properties
function updateSelectedElement(properties) {
    if (!selectedElement) return;
    
    Object.assign(selectedElement, properties);
    redrawCanvas();
}

// Delete selected element
function deleteSelectedElement() {
    if (!selectedElement) return;
    
    const index = textElements.findIndex(el => el.id === selectedElement.id);
    if (index !== -1) {
        textElements.splice(index, 1);
        selectedElement = null;
        redrawCanvas();
    }
}

// Download canvas as image
function downloadCanvasImage(filename = 'image-with-text.png') {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL();
    link.click();
}

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initTextOnImage,
        loadImageOnCanvas,
        addTextElement,
        redrawCanvas,
        updateSelectedElement,
        deleteSelectedElement,
        downloadCanvasImage
    };
}
